import mongoose from 'mongoose';

const enrollmentSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  enrolledAt: {
    type: Date,
    default: Date.now
  },
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  completedLessons: [{
    lessonId: String,
    completedAt: { type: Date, default: Date.now },
    timeSpent: Number, // بالدقائق
    score: Number
  }],
  currentUnit: {
    type: Number,
    default: 0
  },
  currentLesson: {
    type: Number,
    default: 0
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  completedAt: Date,
  finalScore: {
    type: Number,
    min: 0,
    max: 100
  },
  certificateIssued: {
    type: Boolean,
    default: false
  },
  certificateId: String,
  // إحصائيات التعلم
  studyTime: {
    totalMinutes: { type: Number, default: 0 },
    sessionsCount: { type: Number, default: 0 },
    averageSessionTime: { type: Number, default: 0 }
  },
  // تقييم الدورة
  rating: {
    score: { type: Number, min: 1, max: 5 },
    review: String,
    ratedAt: Date
  },
  // ملاحظات المدرب
  instructorNotes: [{
    note: String,
    addedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    addedAt: { type: Date, default: Date.now }
  }]
}, {
  timestamps: true
});

// فهارس للأداء
enrollmentSchema.index({ student: 1, course: 1 }, { unique: true });
enrollmentSchema.index({ student: 1, isCompleted: 1 });
enrollmentSchema.index({ course: 1, isCompleted: 1 });
enrollmentSchema.index({ enrolledAt: -1 });

// حساب التقدم تلقائياً
enrollmentSchema.methods.calculateProgress = function() {
  if (!this.populated('course')) {
    return this.progress;
  }
  
  const course = this.course;
  const totalLessons = course.units.reduce((total, unit) => total + unit.lessons.length, 0);
  
  if (totalLessons === 0) return 0;
  
  const completedCount = this.completedLessons.length;
  this.progress = Math.round((completedCount / totalLessons) * 100);
  
  return this.progress;
};

// تحديث وقت الوصول الأخير
enrollmentSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date();
  return this.save();
};

// إكمال درس
enrollmentSchema.methods.completeLesson = function(lessonId, timeSpent = 0, score = null) {
  const existingLesson = this.completedLessons.find(
    lesson => lesson.lessonId === lessonId
  );
  
  if (!existingLesson) {
    this.completedLessons.push({
      lessonId,
      timeSpent,
      score,
      completedAt: new Date()
    });
    
    // تحديث إجمالي وقت الدراسة
    this.studyTime.totalMinutes += timeSpent;
    this.studyTime.sessionsCount += 1;
    this.studyTime.averageSessionTime = this.studyTime.totalMinutes / this.studyTime.sessionsCount;
  }
  
  this.calculateProgress();
  this.updateLastAccessed();
  
  return this.save();
};

// إكمال الدورة
enrollmentSchema.methods.completeCourse = function(finalScore = null) {
  this.isCompleted = true;
  this.completedAt = new Date();
  this.progress = 100;
  
  if (finalScore !== null) {
    this.finalScore = finalScore;
  }
  
  return this.save();
};

export default mongoose.models.Enrollment || mongoose.model('Enrollment', enrollmentSchema);
