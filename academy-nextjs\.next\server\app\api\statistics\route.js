"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/statistics/route";
exports.ids = ["app/api/statistics/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatistics%2Froute&page=%2Fapi%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatistics%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatistics%2Froute&page=%2Fapi%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatistics%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Abdulmnoum_Downloads_project_bolt_academy_project_academy_nextjs_app_api_statistics_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/statistics/route.js */ \"(rsc)/./app/api/statistics/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/statistics/route\",\n        pathname: \"/api/statistics\",\n        filename: \"route\",\n        bundlePath: \"app/api/statistics/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\api\\\\statistics\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Abdulmnoum_Downloads_project_bolt_academy_project_academy_nextjs_app_api_statistics_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/statistics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatistics%2Froute&page=%2Fapi%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatistics%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/statistics/route.js":
/*!*************************************!*\
  !*** ./app/api/statistics/route.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/mongodb */ \"(rsc)/./lib/mongodb.js\");\n/* harmony import */ var _models_Statistics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../models/Statistics */ \"(rsc)/./models/Statistics.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../models/User */ \"(rsc)/./models/User.js\");\n/* harmony import */ var _models_Course__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../models/Course */ \"(rsc)/./models/Course.js\");\n/* harmony import */ var _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../models/Enrollment */ \"(rsc)/./models/Enrollment.js\");\n\n\n\n\n\n\n\n// GET - جلب الإحصائيات\nconst GET = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.withAuth)(async (request)=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get(\"period\") || \"daily\";\n        const refresh = searchParams.get(\"refresh\") === \"true\";\n        let stats;\n        if (refresh) {\n            // إعادة حساب الإحصائيات\n            stats = await _models_Statistics__WEBPACK_IMPORTED_MODULE_3__[\"default\"].calculateStats(period);\n        } else {\n            // جلب آخر إحصائيات محفوظة\n            stats = await _models_Statistics__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getLatestStats(period);\n            // إذا لم توجد إحصائيات أو كانت قديمة (أكثر من ساعة)\n            if (!stats || new Date() - stats.lastUpdated > 60 * 60 * 1000) {\n                stats = await _models_Statistics__WEBPACK_IMPORTED_MODULE_3__[\"default\"].calculateStats(period);\n            }\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            statistics: stats\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"Statistics fetch error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"خطأ في جلب الإحصائيات\"\n        }, {\n            status: 500\n        });\n    }\n}, {\n    roles: [\n        \"admin\",\n        \"super-admin\"\n    ]\n});\n// POST - حساب إحصائيات مخصصة\nconst POST = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.withAuth)(async (request)=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const { type, filters = {} } = await request.json();\n        let result = {};\n        switch(type){\n            case \"dashboard\":\n                result = await getDashboardStats(filters);\n                break;\n            case \"users\":\n                result = await getUserStats(filters);\n                break;\n            case \"courses\":\n                result = await getCourseStats(filters);\n                break;\n            case \"enrollments\":\n                result = await getEnrollmentStats(filters);\n                break;\n            case \"leaderboard\":\n                result = await getLeaderboardStats(filters);\n                break;\n            default:\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    message: \"نوع الإحصائية غير مدعوم\"\n                }, {\n                    status: 400\n                });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            type,\n            data: result,\n            generatedAt: new Date()\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"Custom statistics error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"خطأ في حساب الإحصائيات المخصصة\"\n        }, {\n            status: 500\n        });\n    }\n}, {\n    roles: [\n        \"admin\",\n        \"super-admin\"\n    ]\n});\n// دالة حساب إحصائيات لوحة التحكم\nasync function getDashboardStats(filters) {\n    const now = new Date();\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n    const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n    const [totalUsers, totalStudents, totalAdmins, totalCourses, activeCourses, totalEnrollments, completedEnrollments, monthlyNewUsers, weeklyActiveUsers, recentEnrollments] = await Promise.all([\n        _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments(),\n        _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            role: \"student\"\n        }),\n        _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            role: {\n                $in: [\n                    \"admin\",\n                    \"super-admin\"\n                ]\n            }\n        }),\n        _models_Course__WEBPACK_IMPORTED_MODULE_5__[\"default\"].countDocuments(),\n        _models_Course__WEBPACK_IMPORTED_MODULE_5__[\"default\"].countDocuments({\n            isActive: true\n        }),\n        _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__[\"default\"].countDocuments(),\n        _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__[\"default\"].countDocuments({\n            isCompleted: true\n        }),\n        _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            createdAt: {\n                $gte: startOfMonth\n            }\n        }),\n        _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            lastActive: {\n                $gte: startOfWeek\n            }\n        }),\n        _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__[\"default\"].find().populate(\"student\", \"name\").populate(\"course\", \"title\").sort({\n            enrolledAt: -1\n        }).limit(10).lean()\n    ]);\n    return {\n        overview: {\n            totalUsers,\n            totalStudents,\n            totalAdmins,\n            totalCourses,\n            activeCourses,\n            totalEnrollments,\n            completedEnrollments,\n            completionRate: totalEnrollments > 0 ? Math.round(completedEnrollments / totalEnrollments * 100) : 0\n        },\n        trends: {\n            monthlyNewUsers,\n            weeklyActiveUsers,\n            userGrowthRate: totalUsers > 0 ? Math.round(monthlyNewUsers / totalUsers * 100) : 0\n        },\n        recentActivity: recentEnrollments\n    };\n}\n// دالة حساب إحصائيات المستخدمين\nasync function getUserStats(filters) {\n    const pipeline = [\n        {\n            $group: {\n                _id: \"$role\",\n                count: {\n                    $sum: 1\n                },\n                activeCount: {\n                    $sum: {\n                        $cond: [\n                            \"$isActive\",\n                            1,\n                            0\n                        ]\n                    }\n                }\n            }\n        }\n    ];\n    const roleStats = await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].aggregate(pipeline);\n    const registrationTrend = await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].aggregate([\n        {\n            $group: {\n                _id: {\n                    year: {\n                        $year: \"$createdAt\"\n                    },\n                    month: {\n                        $month: \"$createdAt\"\n                    }\n                },\n                count: {\n                    $sum: 1\n                }\n            }\n        },\n        {\n            $sort: {\n                \"_id.year\": 1,\n                \"_id.month\": 1\n            }\n        },\n        {\n            $limit: 12\n        }\n    ]);\n    return {\n        roleDistribution: roleStats,\n        registrationTrend,\n        totalUsers: await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments()\n    };\n}\n// دالة حساب إحصائيات الدورات\nasync function getCourseStats(filters) {\n    const [levelStats, categoryStats, popularCourses, enrollmentTrend] = await Promise.all([\n        _models_Course__WEBPACK_IMPORTED_MODULE_5__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: \"$level\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]),\n        _models_Course__WEBPACK_IMPORTED_MODULE_5__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: \"$category\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]),\n        _models_Course__WEBPACK_IMPORTED_MODULE_5__[\"default\"].find({\n            isActive: true\n        }).sort({\n            enrollmentCount: -1\n        }).limit(10).select(\"title instructor enrollmentCount rating\").lean(),\n        _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: {\n                        year: {\n                            $year: \"$enrolledAt\"\n                        },\n                        month: {\n                            $month: \"$enrolledAt\"\n                        }\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id.year\": 1,\n                    \"_id.month\": 1\n                }\n            },\n            {\n                $limit: 12\n            }\n        ])\n    ]);\n    return {\n        levelDistribution: levelStats,\n        categoryDistribution: categoryStats,\n        popularCourses,\n        enrollmentTrend\n    };\n}\n// دالة حساب إحصائيات التسجيلات\nasync function getEnrollmentStats(filters) {\n    const completionStats = await _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__[\"default\"].aggregate([\n        {\n            $group: {\n                _id: \"$isCompleted\",\n                count: {\n                    $sum: 1\n                },\n                averageProgress: {\n                    $avg: \"$progress\"\n                }\n            }\n        }\n    ]);\n    const studyTimeStats = await _models_Enrollment__WEBPACK_IMPORTED_MODULE_6__[\"default\"].aggregate([\n        {\n            $group: {\n                _id: null,\n                totalMinutes: {\n                    $sum: \"$studyTime.totalMinutes\"\n                },\n                averageMinutes: {\n                    $avg: \"$studyTime.totalMinutes\"\n                },\n                totalSessions: {\n                    $sum: \"$studyTime.sessionsCount\"\n                }\n            }\n        }\n    ]);\n    return {\n        completionStats,\n        studyTimeStats: studyTimeStats[0] || {\n            totalMinutes: 0,\n            averageMinutes: 0,\n            totalSessions: 0\n        }\n    };\n}\n// دالة حساب إحصائيات لوحة الشرف\nasync function getLeaderboardStats(filters) {\n    const limit = filters.limit || 20;\n    const topStudents = await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].aggregate([\n        {\n            $match: {\n                role: \"student\"\n            }\n        },\n        {\n            $lookup: {\n                from: \"enrollments\",\n                localField: \"_id\",\n                foreignField: \"student\",\n                as: \"enrollments\"\n            }\n        },\n        {\n            $addFields: {\n                completedCourses: {\n                    $size: {\n                        $filter: {\n                            input: \"$enrollments\",\n                            cond: {\n                                $eq: [\n                                    \"$$this.isCompleted\",\n                                    true\n                                ]\n                            }\n                        }\n                    }\n                },\n                totalPoints: {\n                    $sum: {\n                        $map: {\n                            input: {\n                                $filter: {\n                                    input: \"$enrollments\",\n                                    cond: {\n                                        $eq: [\n                                            \"$$this.isCompleted\",\n                                            true\n                                        ]\n                                    }\n                                }\n                            },\n                            in: {\n                                $ifNull: [\n                                    \"$$this.finalScore\",\n                                    0\n                                ]\n                            }\n                        }\n                    }\n                },\n                totalStudyHours: {\n                    $divide: [\n                        {\n                            $sum: {\n                                $map: {\n                                    input: \"$enrollments\",\n                                    in: {\n                                        $ifNull: [\n                                            \"$$this.studyTime.totalMinutes\",\n                                            0\n                                        ]\n                                    }\n                                }\n                            }\n                        },\n                        60\n                    ]\n                }\n            }\n        },\n        {\n            $project: {\n                name: 1,\n                email: 1,\n                completedCourses: 1,\n                totalPoints: 1,\n                totalStudyHours: {\n                    $round: [\n                        \"$totalStudyHours\",\n                        1\n                    ]\n                },\n                createdAt: 1\n            }\n        },\n        {\n            $sort: {\n                totalPoints: -1\n            }\n        },\n        {\n            $limit: limit\n        }\n    ]);\n    return {\n        topStudents,\n        totalStudents: await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n            role: \"student\"\n        })\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/statistics/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateUser: () => (/* binding */ authenticateUser),\n/* harmony export */   checkRole: () => (/* binding */ checkRole),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   roleCheckers: () => (/* binding */ roleCheckers),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/User */ \"(rsc)/./models/User.js\");\n\n\n\n\nconst JWT_SECRET = \"your-super-secret-jwt-key-here-make-it-long-and-complex\";\n// إنشاء JWT Token\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\n// التحقق من JWT Token\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\n// Middleware للتحقق من المصادقة\nasync function authenticateUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return {\n                error: \"Token مطلوب\",\n                status: 401\n            };\n        }\n        const decoded = verifyToken(token);\n        if (!decoded) {\n            return {\n                error: \"Token غير صالح\",\n                status: 401\n            };\n        }\n        await (0,_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(decoded.id).select(\"-password\");\n        if (!user || !user.isActive) {\n            return {\n                error: \"المستخدم غير موجود أو غير نشط\",\n                status: 401\n            };\n        }\n        return {\n            user,\n            status: 200\n        };\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        return {\n            error: \"خطأ في المصادقة\",\n            status: 500\n        };\n    }\n}\n// التحقق من الصلاحيات\nfunction checkRole(user, allowedRoles) {\n    if (!user || !allowedRoles.includes(user.role)) {\n        return false;\n    }\n    return true;\n}\n// Middleware wrapper للـ API routes\nfunction withAuth(handler, options = {}) {\n    return async (request, context)=>{\n        const authResult = await authenticateUser(request);\n        if (authResult.error) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                message: authResult.error\n            }, {\n                status: authResult.status\n            });\n        }\n        // التحقق من الصلاحيات إذا كانت محددة\n        if (options.roles && !checkRole(authResult.user, options.roles)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                message: \"ليس لديك صلاحية للوصول لهذا المورد\"\n            }, {\n                status: 403\n            });\n        }\n        // إضافة المستخدم إلى السياق\n        request.user = authResult.user;\n        return handler(request, context);\n    };\n}\n// Helper functions للأدوار\nconst roleCheckers = {\n    isStudent: (user)=>user?.role === \"student\",\n    isAdmin: (user)=>user?.role === \"admin\",\n    isSuperAdmin: (user)=>user?.role === \"super-admin\",\n    isAdminOrSuperAdmin: (user)=>[\n            \"admin\",\n            \"super-admin\"\n        ].includes(user?.role)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.js":
/*!************************!*\
  !*** ./lib/mongodb.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = \"mongodb+srv://admin:<EMAIL>/academy?retryWrites=true&w=majority\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env.local\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log(\"✅ Connected to MongoDB\");\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./models/Course.js":
/*!**************************!*\
  !*** ./models/Course.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\n// مخطط الدرس\nconst lessonSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    type: {\n        type: String,\n        enum: [\n            \"video\",\n            \"reading\",\n            \"exercise\"\n        ],\n        required: true\n    },\n    content: String,\n    videoUrl: String,\n    textContent: String,\n    duration: String,\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    _id: true\n});\n// مخطط السؤال للاختبار\nconst questionSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    question: {\n        type: String,\n        required: true\n    },\n    options: [\n        {\n            type: String,\n            required: true\n        }\n    ],\n    correctAnswer: {\n        type: String,\n        required: true\n    },\n    explanation: String\n}, {\n    _id: true\n});\n// مخطط الاختبار\nconst quizSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        default: \"اختبار الوحدة\"\n    },\n    questions: [\n        questionSchema\n    ],\n    passingScore: {\n        type: Number,\n        default: 70\n    },\n    timeLimit: {\n        type: Number,\n        default: 30\n    } // بالدقائق\n}, {\n    _id: false\n});\n// مخطط الوحدة\nconst unitSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: String,\n    lessons: [\n        lessonSchema\n    ],\n    quiz: quizSchema,\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    _id: true\n});\nconst courseSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"عنوان الدورة مطلوب\"\n        ],\n        trim: true\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"وصف الدورة مطلوب\"\n        ]\n    },\n    instructor: {\n        type: String,\n        required: [\n            true,\n            \"اسم المدرب مطلوب\"\n        ],\n        default: \"مدرب المنصة\"\n    },\n    duration: {\n        type: String,\n        default: \"0 دقيقة\"\n    },\n    level: {\n        type: String,\n        enum: [\n            \"مبتدئ\",\n            \"متوسط\",\n            \"متقدم\",\n            \"Beginner\",\n            \"Intermediate\",\n            \"Advanced\"\n        ],\n        default: \"مبتدئ\"\n    },\n    category: {\n        type: String,\n        default: \"عام\"\n    },\n    tags: [\n        {\n            type: String,\n            trim: true\n        }\n    ],\n    image: String,\n    video: String,\n    price: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    isFeatured: {\n        type: Boolean,\n        default: false\n    },\n    // إحصائيات\n    enrollmentCount: {\n        type: Number,\n        default: 0\n    },\n    views: {\n        type: Number,\n        default: 0\n    },\n    rating: {\n        type: Number,\n        default: 0,\n        min: 0,\n        max: 5\n    },\n    ratingCount: {\n        type: Number,\n        default: 0\n    },\n    completionRate: {\n        type: Number,\n        default: 0\n    },\n    // المحتوى\n    units: [\n        unitSchema\n    ],\n    // المنشئ والمحرر\n    createdBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    updatedBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\"\n    }\n}, {\n    timestamps: true\n});\n// إنشاء فهرس للبحث النصي\ncourseSchema.index({\n    title: \"text\",\n    description: \"text\",\n    tags: \"text\",\n    instructor: \"text\"\n});\n// فهارس للأداء\ncourseSchema.index({\n    isActive: 1,\n    createdAt: -1\n});\ncourseSchema.index({\n    level: 1,\n    isActive: 1\n});\ncourseSchema.index({\n    category: 1,\n    isActive: 1\n});\ncourseSchema.index({\n    rating: -1,\n    isActive: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Course || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Course\", courseSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/Course.js\n");

/***/ }),

/***/ "(rsc)/./models/Enrollment.js":
/*!******************************!*\
  !*** ./models/Enrollment.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst enrollmentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    student: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    course: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Course\",\n        required: true\n    },\n    enrolledAt: {\n        type: Date,\n        default: Date.now\n    },\n    progress: {\n        type: Number,\n        default: 0,\n        min: 0,\n        max: 100\n    },\n    completedLessons: [\n        {\n            lessonId: String,\n            completedAt: {\n                type: Date,\n                default: Date.now\n            },\n            timeSpent: Number,\n            score: Number\n        }\n    ],\n    currentUnit: {\n        type: Number,\n        default: 0\n    },\n    currentLesson: {\n        type: Number,\n        default: 0\n    },\n    lastAccessedAt: {\n        type: Date,\n        default: Date.now\n    },\n    isCompleted: {\n        type: Boolean,\n        default: false\n    },\n    completedAt: Date,\n    finalScore: {\n        type: Number,\n        min: 0,\n        max: 100\n    },\n    certificateIssued: {\n        type: Boolean,\n        default: false\n    },\n    certificateId: String,\n    // إحصائيات التعلم\n    studyTime: {\n        totalMinutes: {\n            type: Number,\n            default: 0\n        },\n        sessionsCount: {\n            type: Number,\n            default: 0\n        },\n        averageSessionTime: {\n            type: Number,\n            default: 0\n        }\n    },\n    // تقييم الدورة\n    rating: {\n        score: {\n            type: Number,\n            min: 1,\n            max: 5\n        },\n        review: String,\n        ratedAt: Date\n    },\n    // ملاحظات المدرب\n    instructorNotes: [\n        {\n            note: String,\n            addedBy: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: \"User\"\n            },\n            addedAt: {\n                type: Date,\n                default: Date.now\n            }\n        }\n    ]\n}, {\n    timestamps: true\n});\n// فهارس للأداء\nenrollmentSchema.index({\n    student: 1,\n    course: 1\n}, {\n    unique: true\n});\nenrollmentSchema.index({\n    student: 1,\n    isCompleted: 1\n});\nenrollmentSchema.index({\n    course: 1,\n    isCompleted: 1\n});\nenrollmentSchema.index({\n    enrolledAt: -1\n});\n// حساب التقدم تلقائياً\nenrollmentSchema.methods.calculateProgress = function() {\n    if (!this.populated(\"course\")) {\n        return this.progress;\n    }\n    const course = this.course;\n    const totalLessons = course.units.reduce((total, unit)=>total + unit.lessons.length, 0);\n    if (totalLessons === 0) return 0;\n    const completedCount = this.completedLessons.length;\n    this.progress = Math.round(completedCount / totalLessons * 100);\n    return this.progress;\n};\n// تحديث وقت الوصول الأخير\nenrollmentSchema.methods.updateLastAccessed = function() {\n    this.lastAccessedAt = new Date();\n    return this.save();\n};\n// إكمال درس\nenrollmentSchema.methods.completeLesson = function(lessonId, timeSpent = 0, score = null) {\n    const existingLesson = this.completedLessons.find((lesson)=>lesson.lessonId === lessonId);\n    if (!existingLesson) {\n        this.completedLessons.push({\n            lessonId,\n            timeSpent,\n            score,\n            completedAt: new Date()\n        });\n        // تحديث إجمالي وقت الدراسة\n        this.studyTime.totalMinutes += timeSpent;\n        this.studyTime.sessionsCount += 1;\n        this.studyTime.averageSessionTime = this.studyTime.totalMinutes / this.studyTime.sessionsCount;\n    }\n    this.calculateProgress();\n    this.updateLastAccessed();\n    return this.save();\n};\n// إكمال الدورة\nenrollmentSchema.methods.completeCourse = function(finalScore = null) {\n    this.isCompleted = true;\n    this.completedAt = new Date();\n    this.progress = 100;\n    if (finalScore !== null) {\n        this.finalScore = finalScore;\n    }\n    return this.save();\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Enrollment || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enrollment\", enrollmentSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/Enrollment.js\n");

/***/ }),

/***/ "(rsc)/./models/Statistics.js":
/*!******************************!*\
  !*** ./models/Statistics.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst statisticsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // إحصائيات عامة للنظام\n    totalUsers: {\n        type: Number,\n        default: 0\n    },\n    totalStudents: {\n        type: Number,\n        default: 0\n    },\n    totalAdmins: {\n        type: Number,\n        default: 0\n    },\n    totalCourses: {\n        type: Number,\n        default: 0\n    },\n    activeCourses: {\n        type: Number,\n        default: 0\n    },\n    totalEnrollments: {\n        type: Number,\n        default: 0\n    },\n    completedEnrollments: {\n        type: Number,\n        default: 0\n    },\n    // إحصائيات شهرية\n    monthlyStats: {\n        newUsers: {\n            type: Number,\n            default: 0\n        },\n        newEnrollments: {\n            type: Number,\n            default: 0\n        },\n        completedCourses: {\n            type: Number,\n            default: 0\n        },\n        totalRevenue: {\n            type: Number,\n            default: 0\n        }\n    },\n    // إحصائيات أسبوعية\n    weeklyStats: {\n        activeUsers: {\n            type: Number,\n            default: 0\n        },\n        newEnrollments: {\n            type: Number,\n            default: 0\n        },\n        studyHours: {\n            type: Number,\n            default: 0\n        }\n    },\n    // إحصائيات يومية\n    dailyStats: {\n        activeUsers: {\n            type: Number,\n            default: 0\n        },\n        newUsers: {\n            type: Number,\n            default: 0\n        },\n        completedLessons: {\n            type: Number,\n            default: 0\n        }\n    },\n    // تاريخ آخر تحديث\n    lastUpdated: {\n        type: Date,\n        default: Date.now\n    },\n    // فترة الإحصائية\n    period: {\n        type: String,\n        enum: [\n            \"daily\",\n            \"weekly\",\n            \"monthly\",\n            \"yearly\"\n        ],\n        default: \"daily\"\n    },\n    // تاريخ الإحصائية\n    date: {\n        type: Date,\n        default: Date.now\n    }\n}, {\n    timestamps: true\n});\n// فهارس\nstatisticsSchema.index({\n    date: -1,\n    period: 1\n});\nstatisticsSchema.index({\n    lastUpdated: -1\n});\n// دالة لحساب الإحصائيات\nstatisticsSchema.statics.calculateStats = async function(period = \"daily\") {\n    const User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\");\n    const Course = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Course\");\n    const Enrollment = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enrollment\");\n    try {\n        // حساب الإحصائيات العامة\n        const totalUsers = await User.countDocuments();\n        const totalStudents = await User.countDocuments({\n            role: \"student\"\n        });\n        const totalAdmins = await User.countDocuments({\n            role: {\n                $in: [\n                    \"admin\",\n                    \"super-admin\"\n                ]\n            }\n        });\n        const totalCourses = await Course.countDocuments();\n        const activeCourses = await Course.countDocuments({\n            isActive: true\n        });\n        const totalEnrollments = await Enrollment.countDocuments();\n        const completedEnrollments = await Enrollment.countDocuments({\n            isCompleted: true\n        });\n        // حساب الإحصائيات حسب الفترة\n        const now = new Date();\n        let startDate;\n        switch(period){\n            case \"daily\":\n                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                break;\n            case \"weekly\":\n                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                break;\n            case \"monthly\":\n                startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n                break;\n            case \"yearly\":\n                startDate = new Date(now.getFullYear(), 0, 1);\n                break;\n            default:\n                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        }\n        // إحصائيات الفترة\n        const newUsers = await User.countDocuments({\n            createdAt: {\n                $gte: startDate\n            }\n        });\n        const newEnrollments = await Enrollment.countDocuments({\n            enrolledAt: {\n                $gte: startDate\n            }\n        });\n        const completedCourses = await Enrollment.countDocuments({\n            completedAt: {\n                $gte: startDate\n            }\n        });\n        const activeUsers = await User.countDocuments({\n            lastActive: {\n                $gte: startDate\n            }\n        });\n        // إنشاء أو تحديث الإحصائيات\n        const stats = await this.findOneAndUpdate({\n            period,\n            date: {\n                $gte: startDate,\n                $lt: new Date(startDate.getTime() + 24 * 60 * 60 * 1000)\n            }\n        }, {\n            totalUsers,\n            totalStudents,\n            totalAdmins,\n            totalCourses,\n            activeCourses,\n            totalEnrollments,\n            completedEnrollments,\n            [`${period}Stats`]: {\n                newUsers,\n                newEnrollments,\n                completedCourses,\n                activeUsers\n            },\n            lastUpdated: new Date(),\n            date: startDate\n        }, {\n            upsert: true,\n            new: true\n        });\n        return stats;\n    } catch (error) {\n        console.error(\"Error calculating statistics:\", error);\n        throw error;\n    }\n};\n// دالة للحصول على أحدث الإحصائيات\nstatisticsSchema.statics.getLatestStats = async function(period = \"daily\") {\n    return this.findOne({\n        period\n    }).sort({\n        date: -1\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Statistics || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Statistics\", statisticsSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/Statistics.js\n");

/***/ }),

/***/ "(rsc)/./models/User.js":
/*!************************!*\
  !*** ./models/User.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst userSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"الاسم مطلوب\"\n        ],\n        trim: true\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            \"البريد الإلكتروني مطلوب\"\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"كلمة المرور مطلوبة\"\n        ],\n        minlength: [\n            6,\n            \"كلمة المرور يجب أن تكون 6 أحرف على الأقل\"\n        ]\n    },\n    role: {\n        type: String,\n        enum: [\n            \"student\",\n            \"admin\",\n            \"super-admin\"\n        ],\n        default: \"student\"\n    },\n    avatar: {\n        type: String,\n        default: null\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    loginCount: {\n        type: Number,\n        default: 0\n    },\n    // إحصائيات الطالب\n    enrolledCourses: [\n        {\n            courseId: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: \"Course\"\n            },\n            enrolledAt: {\n                type: Date,\n                default: Date.now\n            },\n            progress: {\n                type: Number,\n                default: 0\n            },\n            completedLessons: [\n                {\n                    type: String\n                }\n            ],\n            lastAccessedAt: {\n                type: Date,\n                default: Date.now\n            },\n            isCompleted: {\n                type: Boolean,\n                default: false\n            },\n            completedAt: Date,\n            finalScore: Number,\n            certificateIssued: {\n                type: Boolean,\n                default: false\n            }\n        }\n    ],\n    completedCourses: [\n        {\n            courseId: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: \"Course\"\n            },\n            completedAt: Date,\n            level: String,\n            score: Number,\n            certificateId: String\n        }\n    ],\n    totalPoints: {\n        type: Number,\n        default: 0\n    },\n    achievements: [\n        {\n            title: String,\n            description: String,\n            earnedAt: {\n                type: Date,\n                default: Date.now\n            },\n            icon: String\n        }\n    ],\n    // إحصائيات إضافية\n    stats: {\n        totalStudyHours: {\n            type: Number,\n            default: 0\n        },\n        currentStreak: {\n            type: Number,\n            default: 0\n        },\n        longestStreak: {\n            type: Number,\n            default: 0\n        },\n        lastStudyDate: Date,\n        averageScore: {\n            type: Number,\n            default: 0\n        },\n        coursesStarted: {\n            type: Number,\n            default: 0\n        },\n        coursesCompleted: {\n            type: Number,\n            default: 0\n        }\n    },\n    // تفضيلات المستخدم\n    preferences: {\n        theme: {\n            type: String,\n            enum: [\n                \"light\",\n                \"dark\"\n            ],\n            default: \"light\"\n        },\n        language: {\n            type: String,\n            default: \"ar\"\n        },\n        notifications: {\n            type: Boolean,\n            default: true\n        }\n    }\n}, {\n    timestamps: true\n});\n// Hash password before saving\nuserSchema.pre(\"save\", async function(next) {\n    if (!this.isModified(\"password\")) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Compare password method\nuserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(candidatePassword, this.password);\n};\n// Remove password from JSON output\nuserSchema.methods.toJSON = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", userSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatistics%2Froute&page=%2Fapi%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatistics%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();