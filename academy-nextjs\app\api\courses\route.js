import { NextResponse } from 'next/server';
import { withAuth } from '../../../lib/auth';
import connectDB from '../../../lib/mongodb';
import Course from '../../../models/Course';

// GET - جلب جميع الدورات (عام)
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const search = searchParams.get('search') || '';
    const level = searchParams.get('level') || '';
    const category = searchParams.get('category') || '';
    
    // بناء الاستعلام
    const query = { isActive: true };
    
    // البحث النصي
    if (search) {
      query.$text = { $search: search };
    }
    
    // فلترة حسب المستوى
    if (level) {
      query.level = level;
    }
    
    // فلترة حسب التصنيف
    if (category) {
      query.category = category;
    }
    
    // حساب التخطي
    const skip = (page - 1) * limit;
    
    // جلب الدورات
    const courses = await Course.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name')
      .select('-units'); // إخفاء المحتوى التفصيلي
    
    // عدد الدورات الإجمالي
    const total = await Course.countDocuments(query);
    
    return NextResponse.json({
      courses,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        count: courses.length,
        totalCourses: total
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Courses fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الدورات' },
      { status: 500 }
    );
  }
}

// POST - إنشاء دورة جديدة (للمدراء فقط)
export const POST = withAuth(async (request) => {
  try {
    await connectDB();
    
    const data = await request.json();
    const {
      title,
      description,
      instructor,
      duration,
      level,
      category,
      tags,
      price,
      units
    } = data;

    // التحقق من البيانات المطلوبة
    if (!title || !description || !instructor) {
      return NextResponse.json(
        { message: 'العنوان والوصف واسم المدرب مطلوبة' },
        { status: 400 }
      );
    }

    // إنشاء الدورة
    const course = new Course({
      title: title.trim(),
      description: description.trim(),
      instructor: instructor.trim(),
      duration: duration || '0 دقيقة',
      level: level || 'مبتدئ',
      category: category || 'عام',
      tags: tags || [],
      price: price || 0,
      units: units || [],
      createdBy: request.user._id
    });

    await course.save();

    // جلب الدورة مع بيانات المنشئ
    const populatedCourse = await Course.findById(course._id)
      .populate('createdBy', 'name');

    return NextResponse.json({
      message: 'تم إنشاء الدورة بنجاح',
      course: populatedCourse
    }, { status: 201 });

  } catch (error) {
    console.error('Course creation error:', error);
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: messages.join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'خطأ في إنشاء الدورة' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
