"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/profile/route";
exports.ids = ["app/api/auth/profile/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fprofile%2Froute&page=%2Fapi%2Fauth%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fprofile%2Froute&page=%2Fapi%2Fauth%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Abdulmnoum_Downloads_project_bolt_academy_project_academy_nextjs_app_api_auth_profile_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/profile/route.js */ \"(rsc)/./app/api/auth/profile/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/profile/route\",\n        pathname: \"/api/auth/profile\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/profile/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\api\\\\auth\\\\profile\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Abdulmnoum_Downloads_project_bolt_academy_project_academy_nextjs_app_api_auth_profile_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/profile/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fprofile%2Froute&page=%2Fapi%2Fauth%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/profile/route.js":
/*!***************************************!*\
  !*** ./app/api/auth/profile/route.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/mongodb */ \"(rsc)/./lib/mongodb.js\");\n\n\n\n// GET - الحصول على بيانات المستخدم\nconst GET = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.withAuth)(async (request)=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const user = request.user;\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"تم جلب البيانات بنجاح\",\n            user\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"Profile fetch error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"خطأ في جلب البيانات\"\n        }, {\n            status: 500\n        });\n    }\n});\n// PUT - تحديث بيانات المستخدم\nconst PUT = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.withAuth)(async (request)=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const { name, preferences } = await request.json();\n        const user = request.user;\n        // تحديث البيانات المسموح بها فقط\n        if (name) user.name = name.trim();\n        if (preferences) {\n            user.preferences = {\n                ...user.preferences,\n                ...preferences\n            };\n        }\n        await user.save();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"تم تحديث البيانات بنجاح\",\n            user: user.toJSON()\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"Profile update error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"خطأ في تحديث البيانات\"\n        }, {\n            status: 500\n        });\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/profile/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateUser: () => (/* binding */ authenticateUser),\n/* harmony export */   checkRole: () => (/* binding */ checkRole),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   roleCheckers: () => (/* binding */ roleCheckers),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/User */ \"(rsc)/./models/User.js\");\n\n\n\n\nconst JWT_SECRET = \"your-super-secret-jwt-key-here-make-it-long-and-complex\";\n// إنشاء JWT Token\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\n// التحقق من JWT Token\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\n// Middleware للتحقق من المصادقة\nasync function authenticateUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return {\n                error: \"Token مطلوب\",\n                status: 401\n            };\n        }\n        const decoded = verifyToken(token);\n        if (!decoded) {\n            return {\n                error: \"Token غير صالح\",\n                status: 401\n            };\n        }\n        await (0,_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(decoded.id).select(\"-password\");\n        if (!user || !user.isActive) {\n            return {\n                error: \"المستخدم غير موجود أو غير نشط\",\n                status: 401\n            };\n        }\n        return {\n            user,\n            status: 200\n        };\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        return {\n            error: \"خطأ في المصادقة\",\n            status: 500\n        };\n    }\n}\n// التحقق من الصلاحيات\nfunction checkRole(user, allowedRoles) {\n    if (!user || !allowedRoles.includes(user.role)) {\n        return false;\n    }\n    return true;\n}\n// Middleware wrapper للـ API routes\nfunction withAuth(handler, options = {}) {\n    return async (request, context)=>{\n        const authResult = await authenticateUser(request);\n        if (authResult.error) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                message: authResult.error\n            }, {\n                status: authResult.status\n            });\n        }\n        // التحقق من الصلاحيات إذا كانت محددة\n        if (options.roles && !checkRole(authResult.user, options.roles)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                message: \"ليس لديك صلاحية للوصول لهذا المورد\"\n            }, {\n                status: 403\n            });\n        }\n        // إضافة المستخدم إلى السياق\n        request.user = authResult.user;\n        return handler(request, context);\n    };\n}\n// Helper functions للأدوار\nconst roleCheckers = {\n    isStudent: (user)=>user?.role === \"student\",\n    isAdmin: (user)=>user?.role === \"admin\",\n    isSuperAdmin: (user)=>user?.role === \"super-admin\",\n    isAdminOrSuperAdmin: (user)=>[\n            \"admin\",\n            \"super-admin\"\n        ].includes(user?.role)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.js":
/*!************************!*\
  !*** ./lib/mongodb.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = \"mongodb+srv://admin:<EMAIL>/academy?retryWrites=true&w=majority\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env.local\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log(\"✅ Connected to MongoDB\");\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./models/User.js":
/*!************************!*\
  !*** ./models/User.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst userSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"الاسم مطلوب\"\n        ],\n        trim: true\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            \"البريد الإلكتروني مطلوب\"\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"كلمة المرور مطلوبة\"\n        ],\n        minlength: [\n            6,\n            \"كلمة المرور يجب أن تكون 6 أحرف على الأقل\"\n        ]\n    },\n    role: {\n        type: String,\n        enum: [\n            \"student\",\n            \"admin\",\n            \"super-admin\"\n        ],\n        default: \"student\"\n    },\n    avatar: {\n        type: String,\n        default: null\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    loginCount: {\n        type: Number,\n        default: 0\n    },\n    // إحصائيات الطالب\n    enrolledCourses: [\n        {\n            courseId: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: \"Course\"\n            },\n            enrolledAt: {\n                type: Date,\n                default: Date.now\n            },\n            progress: {\n                type: Number,\n                default: 0\n            },\n            completedLessons: [\n                {\n                    type: String\n                }\n            ],\n            lastAccessedAt: {\n                type: Date,\n                default: Date.now\n            },\n            isCompleted: {\n                type: Boolean,\n                default: false\n            },\n            completedAt: Date,\n            finalScore: Number,\n            certificateIssued: {\n                type: Boolean,\n                default: false\n            }\n        }\n    ],\n    completedCourses: [\n        {\n            courseId: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: \"Course\"\n            },\n            completedAt: Date,\n            level: String,\n            score: Number,\n            certificateId: String\n        }\n    ],\n    totalPoints: {\n        type: Number,\n        default: 0\n    },\n    achievements: [\n        {\n            title: String,\n            description: String,\n            earnedAt: {\n                type: Date,\n                default: Date.now\n            },\n            icon: String\n        }\n    ],\n    // إحصائيات إضافية\n    stats: {\n        totalStudyHours: {\n            type: Number,\n            default: 0\n        },\n        currentStreak: {\n            type: Number,\n            default: 0\n        },\n        longestStreak: {\n            type: Number,\n            default: 0\n        },\n        lastStudyDate: Date,\n        averageScore: {\n            type: Number,\n            default: 0\n        },\n        coursesStarted: {\n            type: Number,\n            default: 0\n        },\n        coursesCompleted: {\n            type: Number,\n            default: 0\n        }\n    },\n    // تفضيلات المستخدم\n    preferences: {\n        theme: {\n            type: String,\n            enum: [\n                \"light\",\n                \"dark\"\n            ],\n            default: \"light\"\n        },\n        language: {\n            type: String,\n            default: \"ar\"\n        },\n        notifications: {\n            type: Boolean,\n            default: true\n        }\n    }\n}, {\n    timestamps: true\n});\n// Hash password before saving\nuserSchema.pre(\"save\", async function(next) {\n    if (!this.isModified(\"password\")) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Compare password method\nuserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(candidatePassword, this.password);\n};\n// Remove password from JSON output\nuserSchema.methods.toJSON = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", userSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fprofile%2Froute&page=%2Fapi%2Fauth%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fprofile%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();