import { NextResponse } from 'next/server';
import { withAuth } from '../../../../../lib/auth';
import connectDB from '../../../../../lib/mongodb';
import Course from '../../../../../models/Course';
import Lesson from '../../../../../models/Lesson';

// GET - جلب وحدات الدورة
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id: courseId } = params;
    
    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // جلب الدروس مجمعة حسب الوحدات
    const lessons = await Lesson.find({ 
      course: courseId,
      isActive: true 
    })
    .sort({ order: 1 })
    .populate('quiz', 'title type settings')
    .lean();

    // تجميع الدروس حسب الوحدات
    const unitsMap = new Map();
    
    lessons.forEach(lesson => {
      const unitId = lesson.unit?.toString() || 'no-unit';
      if (!unitsMap.has(unitId)) {
        unitsMap.set(unitId, {
          _id: lesson.unit,
          title: `الوحدة ${unitsMap.size + 1}`, // يمكن تحسينها بإضافة نموذج Unit منفصل
          lessons: []
        });
      }
      unitsMap.get(unitId).lessons.push(lesson);
    });

    const units = Array.from(unitsMap.values());

    return NextResponse.json({
      units,
      totalLessons: lessons.length
    }, { status: 200 });

  } catch (error) {
    console.error('Units fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب وحدات الدورة' },
      { status: 500 }
    );
  }
});

// POST - إضافة وحدة جديدة
export const POST = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id: courseId } = params;
    const data = await request.json();
    const { title, description, lessons = [] } = data;

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لإضافة وحدات' },
        { status: 403 }
      );
    }

    // إنشاء معرف مؤقت للوحدة (يمكن تحسينها بنموذج Unit منفصل)
    const unitId = new mongoose.Types.ObjectId();
    
    // إنشاء الدروس
    const createdLessons = [];
    for (let i = 0; i < lessons.length; i++) {
      const lessonData = lessons[i];
      const lesson = new Lesson({
        title: lessonData.title,
        description: lessonData.description,
        course: courseId,
        unit: unitId,
        order: i,
        type: lessonData.type || 'text',
        content: lessonData.content || {},
        duration: lessonData.duration || 0,
        isPreview: lessonData.isPreview || false,
        createdBy: request.user._id
      });
      
      await lesson.save();
      createdLessons.push(lesson);
    }

    // تحديث الدورة بإضافة الوحدة (مؤقتاً في مصفوفة units)
    const unitData = {
      _id: unitId,
      title,
      description,
      order: course.units.length,
      lessons: createdLessons.map(l => l._id)
    };

    course.units.push(unitData);
    await course.save();

    return NextResponse.json({
      message: 'تم إضافة الوحدة بنجاح',
      unit: {
        ...unitData,
        lessons: createdLessons
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Unit creation error:', error);
    return NextResponse.json(
      { message: 'خطأ في إضافة الوحدة' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
